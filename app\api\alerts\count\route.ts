import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    // Get low stock and out of stock products count
    const [lowStockCount, outOfStockCount] = await Promise.all([
      prisma.product.count({
        where: {
          stockQuantity: {
            gte: 1,
            lte: 5
          },
          isActive: true
        }
      }),
      prisma.product.count({
        where: {
          stockQuantity: 0,
          isActive: true
        }
      })
    ])

    // Calculate total alert count
    const totalAlerts = lowStockCount + outOfStockCount

    return NextResponse.json({
      totalAlerts,
      lowStockCount,
      outOfStockCount,
      hasAlerts: totalAlerts > 0
    }, { status: 200 })
  } catch (error) {
    console.error('Alerts count API error:', error)
    return NextResponse.json(
      { error: "Failed to fetch alerts count" },
      { status: 500 }
    )
  }
}
