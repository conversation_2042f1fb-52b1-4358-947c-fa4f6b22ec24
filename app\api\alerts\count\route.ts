import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(request: Request) {
  try {
    // Get dismissed alerts from query parameter (sent from client)
    const { searchParams } = new URL(request.url)
    const dismissedAlertsParam = searchParams.get('dismissed')
    const dismissedAlerts = dismissedAlertsParam ? JSON.parse(dismissedAlertsParam) : []

    // Get low stock and out of stock products
    const [lowStockProducts, outOfStockProducts] = await Promise.all([
      prisma.product.findMany({
        where: {
          stockQuantity: { gte: 1, lte: 5 },
          isActive: true
        },
        select: { id: true }
      }),
      prisma.product.findMany({
        where: {
          stockQuantity: 0,
          isActive: true
        },
        select: { id: true }
      })
    ])

    // Filter out dismissed alerts
    const activeLowStockAlerts = lowStockProducts.filter(product =>
      !dismissedAlerts.includes(`out_of_stock_${product.id}`)
    )

    const activeOutOfStockAlerts = outOfStockProducts.filter(product =>
      !dismissedAlerts.includes(`out_of_stock_${product.id}`)
    )

    // Check if low stock warning is dismissed
    const lowStockWarningDismissed = dismissedAlerts.includes('low_stock_warning')
    const hasLowStockWarning = lowStockProducts.length > 0 && !lowStockWarningDismissed

    // Calculate total alert count
    const totalAlerts = activeOutOfStockAlerts.length + (hasLowStockWarning ? 1 : 0)

    return NextResponse.json({
      totalAlerts,
      lowStockCount: activeLowStockAlerts.length,
      outOfStockCount: activeOutOfStockAlerts.length,
      hasAlerts: totalAlerts > 0
    }, { status: 200 })
  } catch (error) {
    console.error('Alerts count API error:', error)
    return NextResponse.json(
      { error: "Failed to fetch alerts count" },
      { status: 500 }
    )
  }
}
