import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    // Fetch all products for catalog without pagination limits
    const products = await prisma.product.findMany({
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Calculate summary statistics
    const summary = {
      totalProducts: products.length,
      activeProducts: products.filter(p => p.isActive).length,
      inactiveProducts: products.filter(p => !p.isActive).length,
      lowStockProducts: products.filter(p => p.stockQuantity > 0 && p.stockQuantity <= 5).length,
      outOfStockProducts: products.filter(p => p.stockQuantity === 0).length,
      totalValue: products.reduce((sum, p) => {
        const price = Number(p.price) || 0
        const stock = Number(p.stockQuantity) || 0
        return sum + (price * stock)
      }, 0),
      averagePrice: products.length > 0 
        ? products.reduce((sum, p) => sum + (Number(p.price) || 0), 0) / products.length 
        : 0
    }

    return NextResponse.json({
      products,
      summary,
      total: products.length
    }, { status: 200 })
  } catch (error) {
    console.error('Product catalog API error:', error)
    return NextResponse.json(
      { error: "Failed to fetch product catalog" },
      { status: 500 }
    )
  }
}
