import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/auth"
import { prisma } from "@/lib/prisma"

// POST /api/auth/sync-oauth-user - Sync OAuth user data
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: "No authenticated user found" },
        { status: 401 }
      )
    }

    const { email } = session.user

    // Find the user in the database
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        accounts: {
          select: {
            provider: true,
            providerAccountId: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      )
    }

    // Check if this is an OAuth user (has accounts)
    const isOAuthUser = user.accounts.length > 0
    const oauthProvider = user.accounts[0]?.provider

    // Update user data to ensure they're properly set up as a customer
    const updatedUser = await prisma.user.update({
      where: { email },
      data: {
        // Ensure OAuth users have USER role (customer)
        role: user.role || 'USER',
        // Auto-verify OAuth users since they're verified by the provider
        emailVerified: user.emailVerified || (isOAuthUser ? new Date() : null),
        // Ensure they're active
        isActive: user.isActive !== false ? true : user.isActive,
        // Update name if it's missing but available in session
        name: user.name || session.user.name || null,
        // Update image if it's missing but available in session
        image: user.image || session.user.image || null
      },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        role: true,
        emailVerified: true,
        isActive: true,
        accounts: {
          select: {
            provider: true
          }
        }
      }
    })

    console.log(`✅ OAuth user synced: ${email} (${oauthProvider || 'unknown provider'})`)

    return NextResponse.json({
      success: true,
      message: "User synced successfully",
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        role: updatedUser.role,
        isOAuthUser,
        provider: oauthProvider,
        isVerified: !!updatedUser.emailVerified,
        isActive: updatedUser.isActive
      }
    })
  } catch (error) {
    console.error('OAuth user sync error:', error)
    return NextResponse.json(
      { error: "Failed to sync user data" },
      { status: 500 }
    )
  }
}

// GET /api/auth/sync-oauth-user - Get current user sync status
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: "No authenticated user found" },
        { status: 401 }
      )
    }

    const { email } = session.user

    // Find the user in the database
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        role: true,
        emailVerified: true,
        isActive: true,
        createdAt: true,
        accounts: {
          select: {
            provider: true,
            providerAccountId: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      )
    }

    const isOAuthUser = user.accounts.length > 0
    const oauthProvider = user.accounts[0]?.provider

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.image,
        role: user.role,
        isOAuthUser,
        provider: oauthProvider,
        isVerified: !!user.emailVerified,
        isActive: user.isActive,
        createdAt: user.createdAt.toISOString()
      },
      needsSync: !user.emailVerified || user.role !== 'USER'
    })
  } catch (error) {
    console.error('Get user sync status error:', error)
    return NextResponse.json(
      { error: "Failed to get user sync status" },
      { status: 500 }
    )
  }
}
