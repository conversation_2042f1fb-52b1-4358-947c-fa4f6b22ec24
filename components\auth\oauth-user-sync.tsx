"use client"

import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"

interface OAuthUserSyncProps {
  children?: React.ReactNode
}

export function OAuthUserSync({ children }: OAuthUserSyncProps) {
  const { data: session, status } = useSession()
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'synced' | 'error'>('idle')

  useEffect(() => {
    const syncOAuthUser = async () => {
      // Only sync if user is authenticated and we haven't synced yet
      if (status === 'authenticated' && session?.user?.email && syncStatus === 'idle') {
        try {
          setSyncStatus('syncing')
          
          // Check if user needs syncing
          const statusResponse = await fetch('/api/auth/sync-oauth-user')
          
          if (statusResponse.ok) {
            const statusData = await statusResponse.json()
            
            // If user needs syncing, perform the sync
            if (statusData.needsSync) {
              console.log('🔄 Syncing OAuth user data...')
              
              const syncResponse = await fetch('/api/auth/sync-oauth-user', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                }
              })
              
              if (syncResponse.ok) {
                const syncData = await syncResponse.json()
                console.log('✅ OAuth user synced successfully:', syncData.user)
                setSyncStatus('synced')
              } else {
                console.error('❌ Failed to sync OAuth user')
                setSyncStatus('error')
              }
            } else {
              console.log('✅ OAuth user already synced')
              setSyncStatus('synced')
            }
          } else {
            console.error('❌ Failed to check sync status')
            setSyncStatus('error')
          }
        } catch (error) {
          console.error('❌ OAuth sync error:', error)
          setSyncStatus('error')
        }
      }
    }

    // Only run sync for authenticated users
    if (status === 'authenticated') {
      syncOAuthUser()
    }
  }, [session, status, syncStatus])

  // This component doesn't render anything visible
  // It just handles the OAuth user syncing in the background
  return children ? <>{children}</> : null
}

// Hook to get sync status
export function useOAuthUserSync() {
  const { data: session, status } = useSession()
  const [syncStatus, setSyncStatus] = useState<{
    status: 'idle' | 'checking' | 'syncing' | 'synced' | 'error'
    user?: any
    needsSync?: boolean
  }>({ status: 'idle' })

  useEffect(() => {
    const checkSyncStatus = async () => {
      if (status === 'authenticated' && session?.user?.email) {
        try {
          setSyncStatus({ status: 'checking' })
          
          const response = await fetch('/api/auth/sync-oauth-user')
          
          if (response.ok) {
            const data = await response.json()
            setSyncStatus({
              status: data.needsSync ? 'syncing' : 'synced',
              user: data.user,
              needsSync: data.needsSync
            })
            
            // If needs sync, perform it
            if (data.needsSync) {
              const syncResponse = await fetch('/api/auth/sync-oauth-user', {
                method: 'POST'
              })
              
              if (syncResponse.ok) {
                const syncData = await syncResponse.json()
                setSyncStatus({
                  status: 'synced',
                  user: syncData.user,
                  needsSync: false
                })
              } else {
                setSyncStatus({ status: 'error' })
              }
            }
          } else {
            setSyncStatus({ status: 'error' })
          }
        } catch (error) {
          setSyncStatus({ status: 'error' })
        }
      }
    }

    if (status === 'authenticated') {
      checkSyncStatus()
    }
  }, [session, status])

  return syncStatus
}
