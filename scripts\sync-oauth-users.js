const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function syncOAuthUsers() {
  try {
    console.log('🔄 Starting OAuth users sync...')
    
    // Find all users who have OAuth accounts (Google, etc.)
    const oauthUsers = await prisma.user.findMany({
      where: {
        accounts: {
          some: {
            provider: {
              not: 'credentials'
            }
          }
        }
      },
      include: {
        accounts: {
          select: {
            provider: true,
            providerAccountId: true
          }
        }
      }
    })

    console.log(`📊 Found ${oauthUsers.length} OAuth users`)

    let syncedCount = 0
    let alreadySyncedCount = 0

    for (const user of oauthUsers) {
      const provider = user.accounts[0]?.provider || 'unknown'
      const needsSync = !user.emailVerified || user.role !== 'USER' || !user.isActive

      if (needsSync) {
        console.log(`🔄 Syncing user: ${user.email} (${provider})`)
        
        await prisma.user.update({
          where: { id: user.id },
          data: {
            // Ensure OAuth users have USER role (customer)
            role: 'USER',
            // Auto-verify OAuth users since they're verified by the provider
            emailVerified: user.emailVerified || new Date(),
            // Ensure they're active
            isActive: true
          }
        })
        
        syncedCount++
        console.log(`✅ Synced: ${user.email}`)
      } else {
        alreadySyncedCount++
        console.log(`✅ Already synced: ${user.email} (${provider})`)
      }
    }

    console.log('\n📊 Sync Summary:')
    console.log(`- Total OAuth users found: ${oauthUsers.length}`)
    console.log(`- Users synced: ${syncedCount}`)
    console.log(`- Already synced: ${alreadySyncedCount}`)

    // Show current customer count
    const customerCount = await prisma.user.count({
      where: {
        role: 'USER'
      }
    })

    console.log(`- Total customers now: ${customerCount}`)

    // Show OAuth customers breakdown
    const oauthCustomers = await prisma.user.findMany({
      where: {
        role: 'USER',
        accounts: {
          some: {
            provider: {
              not: 'credentials'
            }
          }
        }
      },
      include: {
        accounts: {
          select: {
            provider: true
          }
        }
      }
    })

    console.log('\n🔍 OAuth Customers by Provider:')
    const providerCounts = {}
    oauthCustomers.forEach(user => {
      const provider = user.accounts[0]?.provider || 'unknown'
      providerCounts[provider] = (providerCounts[provider] || 0) + 1
    })

    Object.entries(providerCounts).forEach(([provider, count]) => {
      console.log(`- ${provider}: ${count} customers`)
    })

    console.log('\n🎉 OAuth users sync completed successfully!')
    
  } catch (error) {
    console.error('❌ Error syncing OAuth users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the sync
syncOAuthUsers()
