import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Consistent date formatting to prevent hydration errors
export function formatDate(date: string | Date, options?: Intl.DateTimeFormatOptions): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date

  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    ...options
  }

  return dateObj.toLocaleDateString('en-US', defaultOptions)
}

// Format date for display in charts and tables
export function formatDateShort(date: string | Date): string {
  return formatDate(date, {
    month: 'short',
    day: 'numeric'
  })
}

// Format date with time
export function formatDateTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date

  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format currency in Tunisian Dinars with TTC
export function formatCurrency(amount: number): string {
  return `${amount.toLocaleString()} TND TTC`
}

// Format currency for admin/internal use (without TTC)
export function formatCurrencyAdmin(amount: number): string {
  return `${amount.toLocaleString()} TND`
}
