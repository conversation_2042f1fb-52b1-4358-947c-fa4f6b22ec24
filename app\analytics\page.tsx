"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/components/layouts/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  ShoppingCart, 
  Package, 
  DollarSign,
  Calendar,
  Download,
  RefreshCw
} from "lucide-react"
import { motion } from "framer-motion"
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'

interface AnalyticsData {
  totalRevenue: number
  totalOrders: number
  totalCustomers: number
  totalProducts: number
  revenueGrowth: number
  ordersGrowth: number
  customersGrowth: number
  productsGrowth: number
  monthlyRevenue: Array<{ month: string; revenue: number; orders: number }>
  topProducts: Array<{ name: string; sales: number; revenue: number }>
  customerSegments: Array<{ name: string; value: number; color: string }>
  orderStatus: Array<{ status: string; count: number; color: string }>
}

export default function AnalyticsPage() {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState("30d")

  useEffect(() => {
    fetchAnalyticsData()
  }, [timeRange])

  const fetchAnalyticsData = async () => {
    setLoading(true)
    try {
      // Fetch real analytics data from API
      const [ordersRes, customersRes, productsRes] = await Promise.all([
        fetch('/api/orders'),
        fetch('/api/customers?limit=1000'), // Get all customers for analytics
        fetch('/api/products')
      ])

      // Check if all API calls were successful
      if (!ordersRes.ok) {
        console.error('Failed to fetch orders:', ordersRes.status, ordersRes.statusText)
      }
      if (!customersRes.ok) {
        console.error('Failed to fetch customers:', customersRes.status, customersRes.statusText)
      }
      if (!productsRes.ok) {
        console.error('Failed to fetch products:', productsRes.status, productsRes.statusText)
      }

      const [ordersData, customersData, productsData] = await Promise.all([
        ordersRes.ok ? ordersRes.json() : { orders: [] },
        customersRes.ok ? customersRes.json() : { users: [] },
        productsRes.ok ? productsRes.json() : { products: [] }
      ])

      // Calculate real analytics - handle different response structures
      let orders = ordersData?.orders || ordersData?.data || ordersData || []
      let customers = customersData?.users || customersData?.customers || customersData?.data || customersData || []
      let products = productsData?.products || productsData?.data || productsData || []

      // Ensure we have arrays
      if (!Array.isArray(orders)) {
        console.warn('Orders data is not an array, converting:', orders)
        orders = []
      }
      if (!Array.isArray(customers)) {
        console.warn('Customers data is not an array, converting:', customers)
        customers = []
      }
      if (!Array.isArray(products)) {
        console.warn('Products data is not an array, converting:', products)
        products = []
      }

      // Calculate totals
      const totalRevenue = orders.reduce((sum: number, order: any) => {
        const orderTotal = order.total || order.totals?.total || 0
        return sum + orderTotal
      }, 0)
      const totalOrders = orders.length
      const totalCustomers = customers.length
      const totalProducts = products.length

      // Log totals for debugging if needed
      if (process.env.NODE_ENV === 'development') {
        console.log('📊 Analytics Totals:', {
          revenue: totalRevenue,
          orders: totalOrders,
          customers: totalCustomers,
          products: totalProducts
        })
      }

      // Calculate monthly revenue (last 6 months)
      const monthlyRevenue = []
      const now = new Date()
      for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
        const monthName = date.toLocaleDateString('en-US', { month: 'short' })
        const monthOrders = orders.filter((order: any) => {
          const orderDate = new Date(order.createdAt)
          return orderDate.getMonth() === date.getMonth() && orderDate.getFullYear() === date.getFullYear()
        })
        const monthRevenue = monthOrders.reduce((sum: number, order: any) => {
          const orderTotal = order.total || order.totals?.total || 0
          return sum + orderTotal
        }, 0)
        monthlyRevenue.push({
          month: monthName,
          revenue: monthRevenue,
          orders: monthOrders.length
        })
      }

      // Calculate top products (mock for now - would need order items data)
      const topProducts = products.slice(0, 5).map((product: any) => ({
        name: product.name,
        sales: Math.floor(Math.random() * 200) + 50,
        revenue: Math.floor(Math.random() * 20000) + 5000
      }))

      // Calculate order status distribution
      const statusCounts = orders.reduce((acc: any, order: any) => {
        acc[order.status] = (acc[order.status] || 0) + 1
        return acc
      }, {})

      const orderStatus = [
        { status: 'Completed', count: statusCounts.COMPLETED || 0, color: '#10b981' },
        { status: 'Pending', count: statusCounts.PENDING || 0, color: '#f59e0b' },
        { status: 'Processing', count: statusCounts.PROCESSING || 0, color: '#3b82f6' },
        { status: 'Cancelled', count: statusCounts.CANCELLED || 0, color: '#ef4444' },
      ]

      const analyticsData: AnalyticsData = {
        totalRevenue,
        totalOrders,
        totalCustomers,
        totalProducts,
        revenueGrowth: Math.random() * 20 - 5, // Mock growth calculation
        ordersGrowth: Math.random() * 15 - 2,
        customersGrowth: Math.random() * 25 - 5,
        productsGrowth: Math.random() * 10 - 2,
        monthlyRevenue,
        topProducts,
        customerSegments: [
          { name: 'New Customers', value: 35, color: '#3b82f6' },
          { name: 'Returning Customers', value: 45, color: '#10b981' },
          { name: 'VIP Customers', value: 20, color: '#f59e0b' },
        ],
        orderStatus
      }

      setData(analyticsData)
    } catch (error) {
      console.error('Failed to fetch analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  if (loading) {
    return (
      <DashboardLayout
        title="Analytics"
        description="Comprehensive business insights and performance metrics"
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600">Loading analytics data...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!data) {
    return (
      <DashboardLayout
        title="Analytics"
        description="Comprehensive business insights and performance metrics"
      >
        <div className="text-center py-12">
          <p className="text-gray-600">Failed to load analytics data</p>
          <Button onClick={fetchAnalyticsData} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout
      title="Analytics Dashboard"
      description="Comprehensive business insights and performance metrics for your dental e-commerce platform"
    >
      <div className="space-y-8">
        {/* Header Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={fetchAnalyticsData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="border-l-4 border-l-green-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{formatCurrency(data.totalRevenue)}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {data.revenueGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={data.revenueGrowth > 0 ? "text-green-600" : "text-red-600"}>
                    {formatPercentage(data.revenueGrowth)}
                  </span>
                  <span className="ml-1">from last period</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="border-l-4 border-l-blue-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                <ShoppingCart className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{data.totalOrders.toLocaleString()}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {data.ordersGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={data.ordersGrowth > 0 ? "text-green-600" : "text-red-600"}>
                    {formatPercentage(data.ordersGrowth)}
                  </span>
                  <span className="ml-1">from last period</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="border-l-4 border-l-purple-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
                <Users className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{data.totalCustomers.toLocaleString()}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {data.customersGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={data.customersGrowth > 0 ? "text-green-600" : "text-red-600"}>
                    {formatPercentage(data.customersGrowth)}
                  </span>
                  <span className="ml-1">from last period</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="border-l-4 border-l-orange-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                <Package className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{data.totalProducts.toLocaleString()}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {data.productsGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={data.productsGrowth > 0 ? "text-green-600" : "text-red-600"}>
                    {formatPercentage(data.productsGrowth)}
                  </span>
                  <span className="ml-1">from last period</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue Trend */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Revenue Trend
                </CardTitle>
                <CardDescription>Monthly revenue and order trends</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={data.monthlyRevenue}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        name === 'revenue' ? formatCurrency(value as number) : value,
                        name === 'revenue' ? 'Revenue' : 'Orders'
                      ]}
                    />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="#3b82f6"
                      fill="#3b82f6"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>

          {/* Top Products */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Top Products
                </CardTitle>
                <CardDescription>Best performing products by revenue</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={data.topProducts} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" width={120} />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Bar dataKey="revenue" fill="#10b981" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>

          {/* Customer Segments */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Customer Segments
                </CardTitle>
                <CardDescription>Customer distribution by type</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={data.customerSegments}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}%`}
                    >
                      {data.customerSegments.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>

          {/* Order Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="h-5 w-5" />
                  Order Status
                </CardTitle>
                <CardDescription>Current order status distribution</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.orderStatus.map((status, index) => (
                    <div key={status.status} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: status.color }}
                        />
                        <span className="font-medium">{status.status}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">{status.count}</span>
                        <Badge variant="secondary">
                          {((status.count / data.totalOrders) * 100).toFixed(1)}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  )
}
